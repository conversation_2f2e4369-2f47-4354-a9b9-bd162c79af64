{"name": "civic-reporter-backend", "version": "1.0.0", "description": "Backend API for Civic Issue Reporter", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node setup.js", "test": "jest"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.6.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "node-geocoder": "^4.2.0", "nodemailer": "^6.9.7", "openai": "^4.104.0", "sharp": "^0.32.6", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "keywords": ["civic", "reporting", "nodejs", "express", "mongodb"], "author": "Your Name", "license": "MIT"}