<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Location Selection Test</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e5e5e5;
            border-radius: 8px;
        }
        .test-section.success {
            border-color: #10B981;
            background: #F0FDF4;
        }
        .test-section.error {
            border-color: #EF4444;
            background: #FEF2F2;
        }
        #map {
            height: 400px;
            width: 100%;
            border: 2px solid #3B82F6;
            border-radius: 8px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #10B981;
            color: white;
        }
        .status.error {
            background: #EF4444;
            color: white;
        }
        .status.info {
            background: #3B82F6;
            color: white;
        }
        .coordinates {
            background: #F3F4F6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
        button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563EB;
        }
        .instructions {
            background: #FEF3C7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #F59E0B;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Manual Location Selection Test</h1>
        <p>This page tests the manual location selection functionality independently.</p>

        <div class="instructions">
            <strong>📋 Test Instructions:</strong>
            <ol>
                <li>Click anywhere on the map below</li>
                <li>A blue marker should appear at the clicked location</li>
                <li>Try dragging the marker to a new position</li>
                <li>Click elsewhere to change the location</li>
                <li>Check that coordinates update correctly</li>
            </ol>
        </div>

        <div class="test-section" id="mapTest">
            <h2>🎯 Map Click Test</h2>
            <div id="mapStatus" class="status info">Ready to test - Click on the map below</div>
            
            <div id="map"></div>
            
            <div class="coordinates" id="coordinates">
                <strong>Selected Coordinates:</strong> None selected yet
            </div>
            
            <button onclick="clearSelection()">Clear Selection</button>
            <button onclick="testNYC()">Test NYC Location</button>
            <button onclick="testLA()">Test LA Location</button>
        </div>

        <div class="test-section" id="resultsTest">
            <h2>📊 Test Results</h2>
            <div id="testResults">
                <div id="clickTest" class="status info">❓ Click Test: Not tested yet</div>
                <div id="dragTest" class="status info">❓ Drag Test: Not tested yet</div>
                <div id="coordinateTest" class="status info">❓ Coordinate Test: Not tested yet</div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize map
        const map = L.map('map').setView([40.7128, -74.0060], 13);
        
        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        let currentMarker = null;
        let selectedLocation = null;
        let clickTested = false;
        let dragTested = false;

        // Update status
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('mapStatus');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        // Update coordinates display
        function updateCoordinates(lat, lng) {
            const coordEl = document.getElementById('coordinates');
            coordEl.innerHTML = `
                <strong>Selected Coordinates:</strong><br>
                Latitude: ${lat.toFixed(6)}<br>
                Longitude: ${lng.toFixed(6)}
            `;
        }

        // Update test results
        function updateTestResult(testId, status, message) {
            const testEl = document.getElementById(testId);
            testEl.textContent = message;
            testEl.className = `status ${status}`;
        }

        // Add marker at location
        function addMarker(lat, lng) {
            // Remove existing marker
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }

            // Create new marker
            currentMarker = L.marker([lat, lng], {
                draggable: true,
                icon: L.divIcon({
                    className: 'location-marker',
                    html: `
                        <div style="
                            background: #3B82F6;
                            width: 30px;
                            height: 30px;
                            border-radius: 50%;
                            border: 4px solid white;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 16px;
                            cursor: move;
                        ">📍</div>
                    `,
                    iconSize: [30, 30],
                    iconAnchor: [15, 15]
                })
            }).addTo(map);

            // Add popup
            currentMarker.bindPopup(`
                <div style="text-align: center; font-weight: 600;">
                    <div style="color: #3B82F6; margin-bottom: 4px;">📍 Selected Location</div>
                    <div style="font-size: 11px; color: #9CA3AF; margin-top: 4px;">
                        Lat: ${lat.toFixed(6)}<br>
                        Lng: ${lng.toFixed(6)}
                    </div>
                    <div style="font-size: 10px; color: #6B7280; margin-top: 4px;">
                        Drag to adjust position
                    </div>
                </div>
            `);

            // Handle drag
            currentMarker.on('dragend', function(e) {
                const { lat, lng } = e.target.getLatLng();
                selectedLocation = { lat, lng };
                updateCoordinates(lat, lng);
                updateStatus('✅ Marker dragged successfully!', 'success');
                
                if (!dragTested) {
                    updateTestResult('dragTest', 'success', '✅ Drag Test: PASSED - Marker can be dragged');
                    dragTested = true;
                }

                // Update popup
                currentMarker.setPopupContent(`
                    <div style="text-align: center; font-weight: 600;">
                        <div style="color: #3B82F6; margin-bottom: 4px;">📍 Selected Location</div>
                        <div style="font-size: 11px; color: #9CA3AF; margin-top: 4px;">
                            Lat: ${lat.toFixed(6)}<br>
                            Lng: ${lng.toFixed(6)}
                        </div>
                        <div style="font-size: 10px; color: #6B7280; margin-top: 4px;">
                            Drag to adjust position
                        </div>
                    </div>
                `);
            });

            // Update coordinates
            selectedLocation = { lat, lng };
            updateCoordinates(lat, lng);
        }

        // Map click handler
        map.on('click', function(e) {
            const { lat, lng } = e.latlng;
            console.log('🎯 Map clicked at:', { lat, lng });
            
            addMarker(lat, lng);
            updateStatus('✅ Location selected! Try dragging the marker.', 'success');
            
            if (!clickTested) {
                updateTestResult('clickTest', 'success', '✅ Click Test: PASSED - Map responds to clicks');
                clickTested = true;
            }

            updateTestResult('coordinateTest', 'success', '✅ Coordinate Test: PASSED - Coordinates update correctly');
        });

        // Clear selection
        function clearSelection() {
            if (currentMarker) {
                map.removeLayer(currentMarker);
                currentMarker = null;
            }
            selectedLocation = null;
            updateCoordinates(0, 0);
            updateStatus('Selection cleared. Click on the map to select a new location.', 'info');
            
            document.getElementById('coordinates').innerHTML = '<strong>Selected Coordinates:</strong> None selected yet';
        }

        // Test NYC location
        function testNYC() {
            const nycLat = 40.7589;
            const nycLng = -73.9851;
            map.setView([nycLat, nycLng], 15);
            addMarker(nycLat, nycLng);
            updateStatus('✅ NYC test location set!', 'success');
        }

        // Test LA location
        function testLA() {
            const laLat = 34.0522;
            const laLng = -118.2437;
            map.setView([laLat, laLng], 15);
            addMarker(laLat, laLng);
            updateStatus('✅ LA test location set!', 'success');
        }

        // Initial status
        updateStatus('🎯 Click anywhere on the map to test location selection', 'info');
        
        console.log('🗺️ Manual location test page loaded');
        console.log('✅ Map initialized and ready for testing');
    </script>
</body>
</html>